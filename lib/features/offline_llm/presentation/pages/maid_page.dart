import 'package:flutter/material.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/app_preferences.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/artificial_intelligence.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/characters.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/huggingface_selection.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/sessions.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/user.dart';
import 'package:diogeneschatbot/models/maid_properties.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/app.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/app.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:provider/provider.dart';

class MaidApp extends StatefulWidget {
  const MaidApp({super.key});

  @override
  _MaidAppState createState() => _MaidAppState();
}

class _MaidAppState extends State<MaidApp> {
  MaidProperties? props;
  String? _errorMessage;
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    _loadMaidProperties();
  }

  /// Loads MaidProperties with enhanced error handling
  Future<void> _loadMaidProperties() async {
    try {
      setState(() {
        _errorMessage = null;
        _isRetrying = false;
      });

      // Load MaidProperties after the first frame
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        try {
          props = await MaidProperties.last;
          if (mounted) {
            setState(() {});
          }
        } catch (e) {
          if (mounted) {
            setState(() {
              _errorMessage = 'Failed to load Maid properties: ${e.toString()}';
            });
          }
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Initialization failed: ${e.toString()}';
      });
    }
  }

  /// Retries loading MaidProperties
  Future<void> _retryLoading() async {
    setState(() {
      _isRetrying = true;
    });
    await _loadMaidProperties();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = AppTheme.isDarkMode(context);

    // Show error state if there's an error
    if (_errorMessage != null) {
      return Scaffold(
        backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 80,
                  color: Colors.red.withValues(alpha: 0.7),
                ),
                const SizedBox(height: 24),
                Text(
                  'Initialization Error',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  _errorMessage!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: _isRetrying ? null : _retryLoading,
                      icon: _isRetrying
                          ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : Icon(Icons.refresh),
                      label: Text(_isRetrying ? 'Retrying...' : 'Retry'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryGreen,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 16),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text('Go Back'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Show enhanced loading indicator if props is null
    if (props == null) {
      return Scaffold(
        backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              LoadingStyles.gradient(
                size: 80,
                message: 'Initializing Maid AI...',
              ),
              const SizedBox(height: 32),
              Text(
                'Setting up your AI assistant',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'This may take a few moments',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.5),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => props!.appPreferences,
          key: ValueKey('appPreferencesProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.sessions,
          key: ValueKey('sessionsProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.characters,
          key: ValueKey('charactersProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.ai,
          key: ValueKey('aiProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.user,
          key: ValueKey('userProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => HuggingfaceSelection(),
          key: ValueKey(
            'huggingfaceSelectionProvider_maid',
          ), // Adding unique key
        ),
      ],
      child: Selector<AppPreferences, bool>(
        selector: (context, appPreferences) => appPreferences.isDesktop,
        builder: (context, isDesktop, child) {
          if (isDesktop) {
            return const DesktopApp();
          } else {
            return const MobileApp();
          }
        },
      ),
    );
  }
}
